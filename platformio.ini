; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:uno]
monitor_speed = 115200
platform = atmelavr
board = uno
framework = arduino
lib_deps = atrappmann/PN5180 Library@^1.5
