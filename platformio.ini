; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:nano]
platform = atmelavr
board = nanoatmega328
framework = arduino
lib_deps = 
	playfultechnology/PN5180@^1.0.0
	miguelbalboa/MFRC522@^1.4.12
