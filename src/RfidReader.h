#ifndef RFID_READER_H
#define RFID_READER_H

#include <Arduino.h>
#include <SPI.h>
#include <MFRC522.h>

class RfidReader {
private:
  MFRC522 mfrc522;
  byte* targetTag;
  byte targetTagSize;
  unsigned long lastSeenTime;
  unsigned long timeoutMs;
  
public:
  RfidReader(byte ssPin, byte rstPin, byte* targetTagUid, byte tagSize, unsigned long timeout = 2000);
  
  void begin();
  bool isTargetTagPresent();
  bool hasTimedOut();
  void displayDetectedTag();
  void printHexBytes(byte* buffer, byte bufferSize);
  
private:
  bool isTargetTag();
  void updateLastSeenTime();
};

#endif
