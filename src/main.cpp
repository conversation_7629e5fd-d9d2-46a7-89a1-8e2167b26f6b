#include <Arduino.h>
#include <SPI.h>
#include <MFRC522.h>

// MFRC522 RFID Reader Pin Connections for ESP32:
// VCC  -> 3.3V
// GND  -> GND
// RST  -> GPIO 21
// SDA  -> GPIO 5 (SS/Slave Select)
// SCK  -> GPIO 18 (SPI Clock - default ESP32 SPI)
// MOSI -> GPIO 23 (Master Out Slave In - default ESP32 SPI)
// MISO -> GPIO 19 (Master In Slave Out - default ESP32 SPI)
const byte RFID_SS_PIN = 5;
const byte RFID_RST_PIN = 21;

const byte NUM_CANDLES = 2;
const byte candleLedPins[] = {13, 14};
const byte sensorInputPins[] = {12, 27};
const byte correctBlowOrder[] = {0, 1};
const byte lockRelayPin = 22;
const byte failIndicatorPin = 10;

enum PuzzleState {
  INACTIVE,
  ACTIVE,
  SOLVED,
  FAILED
};

byte targetRfidTag[4] = {0x1E, 0xC4, 0xB1, 0x01};

MFRC522 rfidReader(RFID_SS_PIN, RFID_RST_PIN);

int currentSequenceStep = 0;
bool candleIsLit[NUM_CANDLES];
int incorrectAttempts = 0;

PuzzleState currentState = INACTIVE;
bool rfidTagIsPresent = false;
unsigned long lastRfidCheckTime = 0;
const unsigned long RFID_CHECK_INTERVAL_MS = 500;
unsigned long rfidTagLastSeenTime = 0;
const unsigned long RFID_TAG_TIMEOUT_MS = 2000;


void initializePins();
void initializeRfidReader();
void setState(PuzzleState newState);
void handleStateTransitions();
void handlePuzzleCompletion();
void unlockMechanism();
void processCandles();
void processCandleInput(int candleIndex);
void extinguishCandle(int candleIndex);
bool isCorrectCandle(int candleIndex);

bool isTargetRfidTagPresent();
bool isTargetRfidTag();
void updateRfidTagSeenTime();
void displayDetectedTag();
bool hasRfidTagTimedOut();
void printHexBytes(byte *buffer, byte bufferSize);

void setup() {
  Serial.begin(115200);
  delay(1000);
  Serial.println(F("ESP32 Candle Puzzle Starting..."));
  Serial.println(__FILE__ __DATE__);

  initializePins();
  delay(100);
  initializeRfidReader();
  delay(100);
  setState(INACTIVE);

  Serial.println(F("Setup complete. Waiting for RFID tag..."));
}
 
void loop() {
  if (millis() - lastRfidCheckTime >= RFID_CHECK_INTERVAL_MS) {
    lastRfidCheckTime = millis();
    handleStateTransitions();
  }

  if (currentState == ACTIVE) {
    processCandles();
    handlePuzzleCompletion();
  }

  delay(10);
}

void initializePins() {
  for(int i = 0; i < NUM_CANDLES; i++){
    pinMode(sensorInputPins[i], INPUT);
    pinMode(candleLedPins[i], OUTPUT);
  }
  pinMode(lockRelayPin, OUTPUT);
  pinMode(failIndicatorPin, OUTPUT);
}

void initializeRfidReader() {
  SPI.begin();
  rfidReader.PCD_Init();
}

void unlockMechanism() {
  digitalWrite(lockRelayPin, HIGH);
  delay(500);
  digitalWrite(lockRelayPin, LOW);
}

void setState(PuzzleState newState) {
  if (currentState == newState) return;

  Serial.print(F("State change: "));
  Serial.print(currentState);
  Serial.print(F(" -> "));
  Serial.println(newState);

  currentState = newState;

  switch (currentState) {
    case INACTIVE:
      for(int i = 0; i < NUM_CANDLES; i++){
        candleIsLit[i] = false;
        digitalWrite(candleLedPins[i], LOW);
      }
      digitalWrite(failIndicatorPin, LOW);
      break;

    case ACTIVE:
      digitalWrite(failIndicatorPin, LOW);
      for(int i = 0; i < NUM_CANDLES; i++){
        candleIsLit[i] = true;
        digitalWrite(candleLedPins[i], HIGH);
        delay(100);
      }
      currentSequenceStep = 0;
      incorrectAttempts = 0;
      break;

    case SOLVED:
      for(int i = 0; i < NUM_CANDLES; i++){
        candleIsLit[i] = false;
        digitalWrite(candleLedPins[i], LOW);
      }
      digitalWrite(failIndicatorPin, LOW);
      break;

    case FAILED:
      digitalWrite(failIndicatorPin, HIGH);
      break;
  }
}

void handleStateTransitions() {
  bool currentTagPresent = isTargetRfidTagPresent();

  if (currentTagPresent != rfidTagIsPresent) {
    rfidTagIsPresent = currentTagPresent;

    if (rfidTagIsPresent) {
      setState(ACTIVE);
    } else {
      setState(INACTIVE);
    }
  }
}

void processCandles() {
  for(int i = 0; i < NUM_CANDLES; i++){
    if(candleIsLit[i]){
      analogWrite(candleLedPins[i], random(128, 255));
      processCandleInput(i);
    }
  }
}

void processCandleInput(int candleIndex) {
  if(digitalRead(sensorInputPins[candleIndex]) == LOW) {
    delay(20);
    if(digitalRead(sensorInputPins[candleIndex]) == LOW) {
      extinguishCandle(candleIndex);

      if(!isCorrectCandle(candleIndex)) {
        incorrectAttempts++;
      }
      currentSequenceStep++;
    }
  }
}

void extinguishCandle(int candleIndex) {
  candleIsLit[candleIndex] = false;
  digitalWrite(candleLedPins[candleIndex], LOW);
}

bool isCorrectCandle(int candleIndex) {
  return correctBlowOrder[currentSequenceStep] == candleIndex;
}

void handlePuzzleCompletion() {
  if(currentSequenceStep == NUM_CANDLES){
    if(incorrectAttempts > 0) {
      Serial.println(F("Incorrect sequence! Resetting..."));
      setState(FAILED);
      delay(2000);
      setState(ACTIVE);
    } else {
      Serial.println(F("Puzzle Solved!"));
      unlockMechanism();
      setState(SOLVED);
    }
  }
}

bool isTargetRfidTagPresent() {
  if (rfidReader.PICC_IsNewCardPresent() && rfidReader.PICC_ReadCardSerial()) {
    if (isTargetRfidTag()) {
      Serial.println(F("Correct tag detected!"));
      updateRfidTagSeenTime();
      return true;
    } else {
      displayDetectedTag();
      rfidReader.PICC_HaltA();
      rfidReader.PCD_StopCrypto1();
      return false;
    }
  }

  if (hasRfidTagTimedOut()) {
    return false;
  }

  if (currentState == ACTIVE || currentState == SOLVED) {
    return true;
  }

  return false;
}

bool isTargetRfidTag() {
  for (int i = 0; i < 4; i++) {
    if (rfidReader.uid.uidByte[i] != targetRfidTag[i]) {
      return false;
    }
  }
  Serial.println(F("Correct tag detected!"));
  return true;
}

void updateRfidTagSeenTime() {
  rfidTagLastSeenTime = millis();
}

void displayDetectedTag() {
  Serial.print(F("Wrong tag detected: "));
  printHexBytes(rfidReader.uid.uidByte, rfidReader.uid.size);
  Serial.println();
}

bool hasRfidTagTimedOut() {
  return (currentState == ACTIVE || currentState == SOLVED) && (millis() - rfidTagLastSeenTime > RFID_TAG_TIMEOUT_MS);
}

void printHexBytes(byte *buffer, byte bufferSize) {
  for (byte i = 0; i < bufferSize; i++) {
    Serial.print(buffer[i] < 0x10 ? " 0" : " ");
    Serial.print(buffer[i], HEX);
  }
}
