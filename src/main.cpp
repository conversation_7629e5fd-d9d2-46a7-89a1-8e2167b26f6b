#include <Arduino.h>
#include "RfidReader.h"

const byte myUid[4] = {0x1E, 0xC4, 0xB1, 0x01};
RfidReader rfid(5, 21, myUid);

struct Candle {
  byte ledPin;
  byte inputPin;
  byte order;
  bool isLit;
};
Candle candles[] = {{14, 33, 0, false},
                    // {27, 32, 3, false},
                    // {26, 35, 2, false},
                    {25, 34, 1, false}
                  };
const byte numCandles = sizeof(candles) / sizeof(candles[0]);

enum PuzzleState {
  Inactive,
  InProgress,
  Solved,
  Failed
};
PuzzleState puzzleState = Inactive;

int currentStep = 0;
int mistakesMade = 0;

// Non-blocking timing variables
unsigned long lastDebounceTime = 0;
unsigned long failIndicatorStartTime = 0;
unsigned long lockActivationTime = 0;
unsigned long deactivationStartTime = 0;
int currentDeactivationCandle = 0;
bool isShowingFailure = false;
bool isActivatingLock = false;
bool isDeactivating = false;

const byte lockPin = 16;
const byte failIndicatorPin = 17;

void setupCandles();
void setupRFID();
void startPuzzle();
void runPuzzle();
void processCandle(Candle &candle);
void puzzleSolved();
void puzzleFailed();
void deactivatePuzzle();

void setup() {
  Serial.begin(115200);
  Serial.println(__FILE__ __DATE__);

  setupCandles();
  setupRFID();

  pinMode(lockPin, OUTPUT);
  pinMode(failIndicatorPin, OUTPUT);

}

void setupCandles() {
  for (Candle &candle : candles) {
    pinMode(candle.inputPin, INPUT);
    pinMode(candle.ledPin, OUTPUT);
  }
}

void setupRFID() {
  SPI.begin();
  rfid.begin();
}

void loop() {
  bool tagDetected = rfid.tagDetected();
  bool tagRemoved = rfid.tagRemoved();

  // Handle non-blocking timing operations
  handleTimingOperations();

  switch (puzzleState) {
    case Inactive:
      if (tagDetected) {
        startPuzzle();
      }
      break;
    case InProgress:
      runPuzzle();
      if (tagRemoved) {
        deactivatePuzzle();
      }
      break;
    case Solved:
      if (tagRemoved) {
        deactivatePuzzle();
      }
      break;
    case Failed:
      if (tagRemoved) {
        deactivatePuzzle();
      } else if (!isShowingFailure) {
        startPuzzle();
      }
      break;
  }
}

void runPuzzle() {
  for (Candle &candle : candles) {
    processCandle(candle);
  }

  if (currentStep == numCandles) {
    if (mistakesMade > 0) {
      puzzleFailed();
    } else {
      puzzleSolved();
    }
  }
}

void processCandle(Candle &candle) {
  if (candle.isLit) {
    // analogWrite(candle.ledPin, random(128, 255));
    if (digitalRead(candle.inputPin) == LOW) {
      if (millis() - lastDebounceTime > 20) {
        if (digitalRead(candle.inputPin) == LOW) {
          candle.isLit = false;
          digitalWrite(candle.ledPin, LOW);
          if (candle.order != currentStep)
          {
            mistakesMade++;
          }
          currentStep++;
          lastDebounceTime = millis();
        }
      }
    }
  }
}

void startPuzzle() {
  puzzleState = InProgress;
  currentStep = 0;
  mistakesMade = 0;

  // Turn on all candles immediately
  for (Candle &candle : candles) {
    candle.isLit = true;
    digitalWrite(candle.ledPin, HIGH);
  }
}

void puzzleFailed() {
  puzzleState = Failed;
  digitalWrite(failIndicatorPin, HIGH);
  isShowingFailure = true;
  failIndicatorStartTime = millis();
}

void puzzleSolved() {
  puzzleState = Solved;
  digitalWrite(lockPin, HIGH);
  isActivatingLock = true;
  lockActivationTime = millis();
}

void deactivatePuzzle() {
  puzzleState = Inactive;
  digitalWrite(failIndicatorPin, LOW);

  // Start non-blocking candle deactivation sequence
  isDeactivating = true;
  currentDeactivationCandle = 0;
  deactivationStartTime = millis();
}

void handleTimingOperations() {
  unsigned long currentTime = millis();

  // Handle failure indicator timing
  if (isShowingFailure && currentTime - failIndicatorStartTime >= 3000) {
    digitalWrite(failIndicatorPin, LOW);
    isShowingFailure = false;
  }

  // Handle lock activation timing
  if (isActivatingLock && currentTime - lockActivationTime >= 500) {
    digitalWrite(lockPin, LOW);
    isActivatingLock = false;
  }

  // Handle deactivation sequence
  if (isDeactivating && currentTime - deactivationStartTime >= 100) {
    if (currentDeactivationCandle < numCandles) {
      candles[currentDeactivationCandle].isLit = false;
      digitalWrite(candles[currentDeactivationCandle].ledPin, LOW);
      currentDeactivationCandle++;
      deactivationStartTime = currentTime;
    } else {
      isDeactivating = false;
    }
  }
}
