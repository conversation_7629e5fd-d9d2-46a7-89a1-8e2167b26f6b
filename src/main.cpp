/**
 * "Blow Out Candles" Escape Room Puzzle
 * Copyright (c) 2021 Playful Technology
 *
 * Players must blow out candles, as detected by microphone sound sensors.
 * Doing so "extinguishes" them, turning off an LED flame.
 * - If they blow out all candles in the correct order, a relay is energised (e.g. releasing a maglock)
 * - If they blow out candles in the incorrect order, they reignite and the puzzle is reset
 *
 * The puzzle is activated when a specific RFID tag is detected by the MFRC522 reader.
 * If the tag is removed, the puzzle resets and waits for the tag to be placed back.
 */

// INCLUDES
#include <Arduino.h>
#include <SPI.h>
#include <MFRC522.h>

// MFRC522 RFID Reader pins
const byte RC522_SS = A0;   // SPI Slave Select
const byte RC522_RST = A1;  // Reset pin

// CONSTANTS
const byte numCandles = 3;
// Define the GPIO pins connected to the anode of each candle's LED
// To create flickering effect, this pin must support PWM (Arduino Nano pins 3, 5, 6, 9, 10 and 11)
const byte ledPins[] = {6, 5, 3};
// Define the pins connected to the sound sensor inputs. These can be any GPIO pins.
const byte inputPins[] = {7, 4, 2};
// Define the order in which candles should be blown out
const byte order[] = {0, 1, 2};
// This pin will be driven HIGH to release a lock when puzzle is solved
const byte lockPin = A3;
const byte failPin = A4;

// Target RFID tag UID (4 bytes for MIFARE Classic)
// Your specific tag: 1E C4 B1 01 (hex) = 30 196 177 1 (dec)
byte targetTagUID[4] = {0x1E, 0xC4, 0xB1, 0x01};

// GLOBALS
MFRC522 rfid(RC522_SS, RC522_RST); // Instance of the class

// What step of the sequence is the player currently on?
int currentStep = 0;
bool isLit[numCandles];
int mistakesMade = 0;

// Puzzle state variables
bool puzzleActive = false;
bool tagPresent = false;
unsigned long lastTagCheck = 0;
const unsigned long TAG_CHECK_INTERVAL = 500; // Check for tag every 500ms


// Function declarations
void reset();
void onSolve();
bool checkForTargetTag();
void activatePuzzle();
void deactivatePuzzle();

/**
 * Helper routine to dump a byte array as hex values to Serial.
 */
void printHex(byte *buffer, byte bufferSize) {
  for (byte i = 0; i < bufferSize; i++) {
    Serial.print(buffer[i] < 0x10 ? " 0" : " ");
    Serial.print(buffer[i], HEX);
  }
}

void setup() {
  // Initialize serial communication
  Serial.begin(115200);
  Serial.println(__FILE__ __DATE__);

  // Initialize the input pins that have switches attached
  for(int i=0; i<numCandles; i++){
    pinMode(inputPins[i], INPUT);
    pinMode(ledPins[i], OUTPUT);
  }

  // Set the lock pin as output and secure the lock
  pinMode(lockPin, OUTPUT);
  pinMode(failPin, OUTPUT);

  // Initialize SPI and RFID reader
  SPI.begin();
  rfid.PCD_Init();

  Serial.println(F("RFID Reader initialized"));
  Serial.print(F("Target tag: "));
  printHex(targetTagUID, 4);
  Serial.println();
  Serial.println(F("Waiting for target RFID tag to activate puzzle..."));

  // Start with puzzle inactive
  deactivatePuzzle();
}
 
void loop() {
  // Check for RFID tag periodically
  if (millis() - lastTagCheck >= TAG_CHECK_INTERVAL) {
    lastTagCheck = millis();

    bool currentTagPresent = checkForTargetTag();

    // Tag state changed
    if (currentTagPresent != tagPresent) {
      tagPresent = currentTagPresent;

      if (tagPresent) {
        // Tag detected - activate puzzle
        activatePuzzle();
      } else {
        // Tag removed - deactivate puzzle
        deactivatePuzzle();
      }
    }
  }

  // Only run puzzle logic if puzzle is active
  if (puzzleActive) {
    // Loop through all the inputs
    for(int i=0; i<numCandles; i++){

      // Is this candle currently still alight?
      if(isLit[i]){

        // Make the LED flicker
        analogWrite(ledPins[i], random(128, 255));

        // Is this candle being blown?
        if(digitalRead(inputPins[i]) == LOW) {

          // Simple debounce to guard against false readings
          // Wait for short amount of time
          delay(20);
          // And check whether the candle is *still* being blown
          if(digitalRead(inputPins[i]) == LOW) {

            // Extinguish the flame
            isLit[i] = false;
            digitalWrite(ledPins[i], LOW);

            // Was this the correct candle to blow out next in the sequence?
            if(order[currentStep] == i) {
              Serial.print(F("Candle #"));
              Serial.print(i);
              Serial.println(F(" correctly extinguished!"));
            }
            else {
              Serial.print(F("Candle #"));
              Serial.print(i);
              Serial.print(F(" incorrectly extinguished!"));
              Serial.print(F(" (should have been "));
              Serial.print(order[currentStep]);
              Serial.println(")");

              mistakesMade++;
            }

            currentStep++;
          }
        }
      }
    }

    if(currentStep == numCandles){
      if(mistakesMade>0) {
        digitalWrite(failPin, HIGH);
        delay(2000);
        reset();
      }
      else {
        Serial.println(F("Puzzle Solved!"));
        onSolve();
        reset();
      }
    }
  }
}

void onSolve() {
  // Activate the relay to supply power to the maglock
  digitalWrite(lockPin, HIGH);
  // Wait for one second
  delay(1000);
  // De-activate the relay again
  digitalWrite(lockPin, LOW);
}

void reset() {
  // Only reset if puzzle is active
  if (puzzleActive) {
    // (Re)light all the candles
    digitalWrite(failPin, LOW);
    for(int i=0; i< numCandles; i++){
      isLit[i] = true;
      digitalWrite(ledPins[i], HIGH);
      delay(100);
    }
    // Reset the counters
    currentStep = 0;
    mistakesMade = 0;

    Serial.println(F("Puzzle Reset!"));
  } else {
    // If puzzle is inactive, ensure all candles are off
    for(int i=0; i< numCandles; i++){
      isLit[i] = false;
      digitalWrite(ledPins[i], LOW);
    }
    digitalWrite(failPin, LOW);
  }
}

// Check if the target RFID tag is present
bool checkForTargetTag() {
  // Reset the loop if no new card present on the sensor/reader
  if ( ! rfid.PICC_IsNewCardPresent()) {
    return false;
  }

  // Verify if the NUID has been read
  if ( ! rfid.PICC_ReadCardSerial()) {
    return false;
  }

  // Tag detected, check if it matches our target
  bool isTargetTag = true;
  for (int i = 0; i < 4; i++) {
    if (rfid.uid.uidByte[i] != targetTagUID[i]) {
      isTargetTag = false;
      break;
    }
  }

  if (isTargetTag) {
    // Halt PICC and stop encryption
    rfid.PICC_HaltA();
    rfid.PCD_StopCrypto1();
    return true;
  } else {
    // Wrong tag detected - show what was detected
    Serial.print(F("Wrong tag detected: "));
    printHex(rfid.uid.uidByte, rfid.uid.size);
    Serial.println();

    // Halt PICC and stop encryption
    rfid.PICC_HaltA();
    rfid.PCD_StopCrypto1();
    return false;
  }
}

// Activate the puzzle when correct tag is detected
void activatePuzzle() {
  if (!puzzleActive) {
    Serial.println(F("Target RFID tag detected! Activating puzzle..."));
    puzzleActive = true;
    reset(); // This will light up the candles since puzzleActive is now true
  }
}

// Deactivate the puzzle when tag is removed
void deactivatePuzzle() {
  if (puzzleActive) {
    Serial.println(F("RFID tag removed! Deactivating puzzle..."));
  }
  puzzleActive = false;
  reset(); // This will turn off all candles since puzzleActive is now false
}
