#include <Arduino.h>
#include <SPI.h>
#include <MFRC522.h>

const byte RFID_SS_PIN = A0;
const byte RFID_RST_PIN = A1;

const byte NUM_CANDLES = 2;
const byte candleLedPins[] = {9, 6};
const byte sensorInputPins[] = {8, 7};
const byte correctBlowOrder[] = {0, 1};
const byte lockRelayPin = A3;
const byte failIndicatorPin = A4;

byte targetRfidTag[4] = {0x1E, 0xC4, 0xB1, 0x01};

MFRC522 rfidReader(RFID_SS_PIN, RFID_RST_PIN);

int currentSequenceStep = 0;
bool candleIsLit[NUM_CANDLES];
int incorrectAttempts = 0;

bool puzzleIsActive = false;
bool rfidTagIsPresent = false;
bool puzzleWasSolved = false;
unsigned long lastRfidCheckTime = 0;
const unsigned long RFID_CHECK_INTERVAL_MS = 500;
unsigned long rfidTagLastSeenTime = 0;
const unsigned long RFID_TAG_TIMEOUT_MS = 2000;


void initializePins();
void initializeRfidReader();
void resetPuzzleState();
void activatePuzzle();
void deactivatePuzzle();
void handleTagPlaced();
void handleTagRemoved();
void handlePuzzleCompletion();
void unlockMechanism();
void processCandles();
void processCandleInput(int candleIndex);
void extinguishCandle(int candleIndex);
bool isCorrectCandle(int candleIndex);
void handleCorrectCandle(int candleIndex);
void handleIncorrectCandle(int candleIndex);
bool checkForTargetRfidTag();
bool isTargetRfidTag();
void updateRfidTagSeenTime();
void displayDetectedTag();
bool hasRfidTagTimedOut();
void printHexBytes(byte *buffer, byte bufferSize);

void printHexBytes(byte *buffer, byte bufferSize) {
  for (byte i = 0; i < bufferSize; i++) {
    Serial.print(buffer[i] < 0x10 ? " 0" : " ");
    Serial.print(buffer[i], HEX);
  }
}

void setup() {
  Serial.begin(9600);
  Serial.println(__FILE__ __DATE__);

  initializePins();
  initializeRfidReader();
  deactivatePuzzle();
}
 
void loop() {
  if (millis() - lastRfidCheckTime >= RFID_CHECK_INTERVAL_MS) {
    lastRfidCheckTime = millis();

    bool currentTagPresent = checkForTargetRfidTag();

    if (currentTagPresent != rfidTagIsPresent) {
      rfidTagIsPresent = currentTagPresent;

      if (rfidTagIsPresent) {
        handleTagPlaced();
      } else {
        handleTagRemoved();
      }
    }
  }

  if (puzzleIsActive) {
    processCandles();
    handlePuzzleCompletion();
  }
}

void initializePins() {
  for(int i = 0; i < NUM_CANDLES; i++){
    pinMode(sensorInputPins[i], INPUT);
    pinMode(candleLedPins[i], OUTPUT);
  }
  pinMode(lockRelayPin, OUTPUT);
  pinMode(failIndicatorPin, OUTPUT);
}

void initializeRfidReader() {
  SPI.begin();
  rfidReader.PCD_Init();

  Serial.println(F("RFID Reader initialized"));
  Serial.print(F("Target tag: "));
  printHexBytes(targetRfidTag, 4);
  Serial.println();
  Serial.println(F("Waiting for target RFID tag to activate puzzle..."));
}

void unlockMechanism() {
  digitalWrite(lockRelayPin, HIGH);
  delay(500);
  digitalWrite(lockRelayPin, LOW);
}

void resetPuzzleState() {
  if (puzzleIsActive) {
    digitalWrite(failIndicatorPin, LOW);
    for(int i = 0; i < NUM_CANDLES; i++){
      candleIsLit[i] = true;
      digitalWrite(candleLedPins[i], HIGH);
      delay(100);
    }
    currentSequenceStep = 0;
    incorrectAttempts = 0;
    Serial.println(F("Puzzle Reset!"));
  } else {
    for(int i = 0; i < NUM_CANDLES; i++){
      candleIsLit[i] = false;
      digitalWrite(candleLedPins[i], LOW);
    }
    digitalWrite(failIndicatorPin, LOW);
  }
}

void processCandles() {
  for(int i = 0; i < NUM_CANDLES; i++){
    if(candleIsLit[i]){
      analogWrite(candleLedPins[i], random(128, 255));
      processCandleInput(i);
    }
  }
}

void processCandleInput(int candleIndex) {
  if(digitalRead(sensorInputPins[candleIndex]) == LOW) {
    delay(20);
    if(digitalRead(sensorInputPins[candleIndex]) == LOW) {
      extinguishCandle(candleIndex);

      if(isCorrectCandle(candleIndex)) {
        handleCorrectCandle(candleIndex);
      } else {
        handleIncorrectCandle(candleIndex);
      }

      currentSequenceStep++;
    }
  }
}

void extinguishCandle(int candleIndex) {
  candleIsLit[candleIndex] = false;
  // digitalWrite(candleLedPins[candleIndex], LOW);
}

bool isCorrectCandle(int candleIndex) {
  return correctBlowOrder[currentSequenceStep] == candleIndex;
}

void handleCorrectCandle(int candleIndex) {
  Serial.print(F("Candle #"));
  Serial.print(candleIndex);
  Serial.println(F(" correctly extinguished!"));
}

void handleIncorrectCandle(int candleIndex) {
  Serial.print(F("Candle #"));
  Serial.print(candleIndex);
  Serial.print(F(" incorrectly extinguished! (should have been "));
  Serial.print(correctBlowOrder[currentSequenceStep]);
  Serial.println(")");
  incorrectAttempts++;
}

void handlePuzzleCompletion() {
  if(currentSequenceStep == NUM_CANDLES){
    if(incorrectAttempts > 0) {
      digitalWrite(failIndicatorPin, HIGH);
      delay(2000);
      resetPuzzleState();
    } else {
      Serial.println(F("Puzzle Solved!"));
      unlockMechanism();
      puzzleWasSolved = true;
      deactivatePuzzle();
      Serial.println(F("Remove and replace RFID tag to play again"));
    }
  }
}

bool checkForTargetRfidTag() {
  if (rfidReader.PICC_IsNewCardPresent() && rfidReader.PICC_ReadCardSerial()) {
    if (isTargetRfidTag()) {
      updateRfidTagSeenTime();
      return true;
    } else {
      displayDetectedTag();
      rfidReader.PICC_HaltA();
      rfidReader.PCD_StopCrypto1();
      return false;
    }
  }

  if (hasRfidTagTimedOut()) {
    return false;
  }

  if (puzzleIsActive) {
    return true;
  }

  return false;
}

bool isTargetRfidTag() {
  for (int i = 0; i < 4; i++) {
    if (rfidReader.uid.uidByte[i] != targetRfidTag[i]) {
      return false;
    }
  }
  return true;
}

void updateRfidTagSeenTime() {
  rfidTagLastSeenTime = millis();
}

void displayDetectedTag() {
  Serial.print(F("Wrong tag detected: "));
  printHexBytes(rfidReader.uid.uidByte, rfidReader.uid.size);
  Serial.println();
}

bool hasRfidTagTimedOut() {
  return puzzleIsActive && (millis() - rfidTagLastSeenTime > RFID_TAG_TIMEOUT_MS);
}

void activatePuzzle() {
  if (!puzzleIsActive) {
    Serial.println(F("Target RFID tag detected! Activating puzzle..."));
    puzzleIsActive = true;
    updateRfidTagSeenTime();
    resetPuzzleState();
  }
}

void deactivatePuzzle() {
  if (puzzleIsActive) {
    Serial.println(F("RFID tag removed! Deactivating puzzle..."));
  }
  puzzleIsActive = false;
  resetPuzzleState();
}

void handleTagPlaced() {
  if (puzzleWasSolved) {
    Serial.println(F("Puzzle was already solved. Remove tag first to reset."));
  } else {
    activatePuzzle();
  }
}

void handleTagRemoved() {
  if (puzzleWasSolved) {
    puzzleWasSolved = false;
    Serial.println(F("Tag removed. Ready for new game when tag is placed back."));
  }
  deactivatePuzzle();
}
