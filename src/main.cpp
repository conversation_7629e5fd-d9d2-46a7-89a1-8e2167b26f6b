#include <Arduino.h>
#include <SPI.h>
#include <MFRC522.h>

struct Candle {
  byte ledPin;
  byte inputPin;
  byte order;
  bool isLit;
};

// struct RfidPins {
//   byte nss = 5;    // Yellow
//   byte sck = 18;    // Green
//   byte mosi = 23;   // Blue
//   byte miso = 19;   // Purple
//   byte rst = 21;    // Brown
// } rfidPins;


struct RfidReader {
  MFRC522 reader;
  byte allowedUid[4];
  byte allowedUidSize;
  unsigned long lastCardSeen;

  RfidReader(byte nssPin, byte rstPin, const byte* uid)
    : reader(nssPin, rstPin), allowedUidSize(4), lastCardSeen(0) {
    memcpy(allowedUid, uid, allowedUidSize);
  }

  void begin() {
    reader.PCD_Init();
  }

  bool tagDetected() {
    if (reader.PICC_IsNewCardPresent() && reader.PICC_ReadCardSerial()) {
      if (reader.uid.size == allowedUidSize &&
          memcmp(reader.uid.uidByte, allowedUid, allowedUidSize) == 0) {
        lastCardSeen = millis();
        return true;
      }
    }
    return false;
  }

  bool tagRemoved() {
    return millis() - lastCardSeen > 1000;
  }

  void displayDetectedTag() {
    Serial.print(F("Tag detected: "));
    for (byte i = 0; i < reader.uid.size; i++) {
      Serial.print(reader.uid.uidByte[i] < 0x10 ? " 0" : " ");
      Serial.print(reader.uid.uidByte[i], HEX);
    }
    Serial.println();
  }
};

const byte myUid[4] = {0x1E, 0xC4, 0xB1, 0x01};
RfidReader rfid(5, 21, myUid);  


enum PuzzleState {
  Inactive,
  InProgress,
  Solved,
  Failed
};

PuzzleState puzzleState = Inactive;

Candle candles[] = {{14, 33, 0, false},
                    // {27, 32, 3, false},
                    // {26, 35, 2, false},
                    {25, 34, 1, false}
                  };
const byte numCandles = sizeof(candles) / sizeof(candles[0]);

const byte lockPin = 16;
const byte failIndicatorPin = 17;

int currentStep = 0;
int mistakesMade = 0;

const byte allowedUid[4] = {0x1E, 0xC4, 0xB1, 0x01}; // Replace with real UID
const byte allowedUidSize = sizeof(allowedUid);

void setupCandles();
void setupRFID();
void startPuzzle();
void runPuzzle();
void processCandle(Candle &candle);
void puzzleSolved();
void puzzleFailed();
void deactivatePuzzle();

void setup() {
  Serial.begin(115200);
  Serial.println(__FILE__ __DATE__);

  setupCandles();
  setupRFID();

  pinMode(lockPin, OUTPUT);
  pinMode(failIndicatorPin, OUTPUT);

}

void setupCandles() {
  for (Candle &candle : candles) {
    pinMode(candle.inputPin, INPUT);
    pinMode(candle.ledPin, OUTPUT);
  }
}

void setupRFID() {
  SPI.begin();
  rfid.begin();
}

void loop() {
  bool tagDetected = rfid.tagDetected();
  bool tagRemoved = rfid.tagRemoved();

  switch (puzzleState) {
    case Inactive:
      if (tagDetected) {
        startPuzzle();
      }
      break;
    case InProgress:
      runPuzzle();
      if (tagRemoved) { deactivatePuzzle(); }
      break;
    case Solved:
      if (tagRemoved) { deactivatePuzzle(); }
      break;
    case Failed:
      if (tagRemoved) {
        deactivatePuzzle();
      } else {
        startPuzzle();
      }
      break;
  }
}

void runPuzzle() {
  for (Candle &candle : candles) {
    processCandle(candle);
  }

  if (currentStep == numCandles) {
    if (mistakesMade > 0) {
      puzzleFailed();
    } else {
      puzzleSolved();
    }
  }
}

void processCandle(Candle &candle)
{
  if (candle.isLit) {
    // analogWrite(candle.ledPin, random(128, 255));
    if (digitalRead(candle.inputPin) == LOW) {
      delay(20);
      if (digitalRead(candle.inputPin) == LOW) {
        candle.isLit = false;
        digitalWrite(candle.ledPin, LOW);
        if (candle.order != currentStep)
        {
          mistakesMade++;
        }
        currentStep++;
      }
    }
  }
}

void startPuzzle() {
  puzzleState = InProgress;
  for (Candle &candle : candles) {
    candle.isLit = true;
    digitalWrite(candle.ledPin, HIGH);
    delay(100);
  }

  currentStep = 0;
  mistakesMade = 0;
}

void puzzleFailed() {
  puzzleState = Failed;
  digitalWrite(failIndicatorPin, HIGH);
  delay(3000);
  digitalWrite(failIndicatorPin, LOW);
}

void puzzleSolved() {
  puzzleState = Solved;
  digitalWrite(lockPin, HIGH);
  delay(500);
  digitalWrite(lockPin, LOW);
}

void deactivatePuzzle() {
  puzzleState = Inactive;
  digitalWrite(failIndicatorPin, LOW);
  for (Candle &candle : candles) {
    candle.isLit = false;
    digitalWrite(candle.ledPin, LOW);
    delay(100);
  }
}
