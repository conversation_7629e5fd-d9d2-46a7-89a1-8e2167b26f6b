#include <Arduino.h>
#include "RfidReader.h"

const byte myUid[4] = {0x1E, 0xC4, 0xB1, 0x01};
RfidReader rfid(5, 21, myUid);

struct Candle {
  byte ledPin;
  byte inputPin;
  byte order;
  bool isLit;
};
Candle candles[] = {{14, 33, 0, false},
                    // {27, 32, 3, false},
                    // {26, 35, 2, false},
                    {25, 34, 1, false}
                  };
const byte numCandles = sizeof(candles) / sizeof(candles[0]);

enum PuzzleState {
  Inactive,
  InProgress,
  Solved,
  Failed
};
PuzzleState puzzleState = Inactive;

unsigned long lastStateChange  = 0;
unsigned long failIndicatorDuration = 2000;
unsigned long lockDuration = 500;

int currentStep = 0;
int mistakesMade = 0;

const byte lockPin = 16;
const byte failIndicatorPin = 17;

void setupCandles();
void setupRFID();
void startPuzzle();
void runPuzzle();
void processCandle(Candle &candle);
void puzzleSolved();
void puzzleFailed();
void deactivatePuzzle();
void updateState(PuzzleState newState);
void resetPuzzleWithLightsOn(bool lightsOn);

void setup() {
  Serial.begin(115200);
  Serial.println(__FILE__ __DATE__);

  setupCandles();
  setupRFID();

  pinMode(lockPin, OUTPUT);
  pinMode(failIndicatorPin, OUTPUT);

}

void setupCandles() {
  for (Candle &candle : candles) {
    pinMode(candle.inputPin, INPUT);
    pinMode(candle.ledPin, OUTPUT);
  }
}

void setupRFID() {
  SPI.begin();
  rfid.begin();
}

void loop() {
  bool tagDetected = rfid.tagDetected();
  bool tagRemoved = rfid.tagRemoved();

  switch (puzzleState) {
    case Inactive:
      if (tagDetected) {
        startPuzzle();
      }
      break;
    case InProgress:
      runPuzzle();
      if (tagRemoved) {
        deactivatePuzzle();
      }
      break;
    case Solved:
      if (tagRemoved) {
        deactivatePuzzle();
      } else if (millis() - lastStateChange > lockDuration) {
        digitalWrite(lockPin, LOW);
      }
      break;
    case Failed:
      if (tagRemoved) {
        deactivatePuzzle();
      } else if (millis() - lastStateChange > failIndicatorDuration) {
        startPuzzle();
      }
      break;
  }
}

void runPuzzle() {
  for (Candle &candle : candles) {
    processCandle(candle);
  }

  if (currentStep == numCandles) {
    if (mistakesMade > 0) {
      puzzleFailed();
    } else {
      puzzleSolved();
    }
  }
}

void processCandle(Candle &candle) {
  if (candle.isLit) {
    // analogWrite(candle.ledPin, random(128, 255));
    if (digitalRead(candle.inputPin) == LOW) {
      delay(20);
      if (digitalRead(candle.inputPin) == LOW) {
        candle.isLit = false;
        digitalWrite(candle.ledPin, LOW);
        if (candle.order != currentStep)
        {
          mistakesMade++;
        }
        currentStep++;
      }
    }
  }
}

void startPuzzle() {
  updateState(InProgress);
  resetPuzzleWithLightsOn(true);
}

void puzzleFailed() {
  updateState(Failed);
  digitalWrite(failIndicatorPin, HIGH);
}

void puzzleSolved() {
  updateState(Solved);
  digitalWrite(lockPin, HIGH);
}

void deactivatePuzzle() {
  updateState(Inactive);
  resetPuzzleWithLightsOn(false);
}

void updateState(PuzzleState newState) {
  lastStateChange = millis();
  puzzleState = newState;
}

void resetPuzzleWithLightsOn(bool lightsOn) {
  digitalWrite(failIndicatorPin, LOW);
  digitalWrite(lockPin, LOW);
  for (Candle &candle : candles) {
    candle.isLit = lightsOn;
    digitalWrite(candle.ledPin, lightsOn ? HIGH : LOW);
  }

  currentStep = 0;
  mistakesMade = 0;
}
