/**
 * "Blow Out Candles" Escape Room Puzzle
 * Copyright (c) 2021 Playful Technology
 *
 * Players must blow out candles, as detected by microphone sound sensors.
 * Doing so "extinguishes" them, turning off an LED flame.
 * - If they blow out all candles in the correct order, a relay is energised (e.g. releasing a maglock)
 * - If they blow out candles in the incorrect order, they reignite and the puzzle is reset
 */

// INCLUDES
#include <Arduino.h>

// CONSTANTS
const byte numCandles = 4;
// Define the GPIO pins connected to the anode of each candle's LED
// To create flickering effect, this pin must support PWM (UNO/Nano pins 3, 5, 6, 9, 10 and 11)
const byte ledPins[] = {9, 6, 5, 3};
// Define the pins connected to the sound sensor inputs. These can be any GPIO pins.
const byte inputPins[] = {8, 7, 4, 2};
// Define the order in which candles should be blown out
const byte order[] = {0, 1, 2, 3};
// This pin will be driven HIGH to release a lock when puzzle is solved
const byte lockPin = A3;
const byte failPin = A4;

// GLOBALS
// What step of the sequence is the player currently on?
int currentStep = 0;
bool isLit[numCandles];
int mistakesMade = 0;

void reset();
void onSolve();

void setup() {
  // Initialize serial communication
  Serial.begin(115200);
  Serial.println(__FILE__ __DATE__);

  // Initialise the input pins that have switches attached
  for(int i=0; i<numCandles; i++){
    pinMode(inputPins[i], INPUT);
    pinMode(ledPins[i], OUTPUT);
  }

  // Set the lock pin as output and secure the lock
  pinMode(lockPin, OUTPUT);
  pinMode(failPin, OUTPUT);

  // Reset the puzzle to its default state
  reset();
}

void onSolve() {
  // Activate the relay to supply power to the maglock
  digitalWrite(lockPin, HIGH);
  // Wait for one second
  delay(500);
  // De-activate the relay again
  digitalWrite(lockPin, LOW);
}


void reset() {

  // (Re)light all the candles
  digitalWrite(failPin, LOW);
  for(int i=0; i< numCandles; i++){
    isLit[i] = true;
    digitalWrite(ledPins[i], HIGH);
    delay(100);
  }
  // Reset the counters
  currentStep = 0;
  mistakesMade = 0;

  Serial.println(F("Puzzle Reset!"));
}


void loop() {

  // Loop through all the inputs
  for(int i=0; i<numCandles; i++){

    // Is this candle currently still alight?
    if(isLit[i]){

      // Make the LED flicker
      analogWrite(ledPins[i], random(128, 255));

      // Is this candle being blown?
      if(digitalRead(inputPins[i]) == LOW) {

        // Simple debounce to guard against false readings
        // Wait for short amount of time
        delay(20);
        // And check whether the candle is *still* being blown
        if(digitalRead(inputPins[i]) == LOW) {

          // Extinguish the flame
          isLit[i] = false;
          digitalWrite(ledPins[i], LOW);

          // Was this the correct candle to blow out next in the sequence?
          if(order[currentStep] == i) {
            Serial.print(F("Candle #"));
            Serial.print(i);
            Serial.println(F(" correctly extinguished!"));
          }
          else {
            Serial.print(F("Candle #"));
            Serial.print(i);
            Serial.print(F(" incorrectly extinguished!"));
            Serial.print(F(" (should have been "));
            Serial.print(order[currentStep]);
            Serial.println(")");

            mistakesMade++;
          }

          currentStep++;
        }
      }
    }
  }
  if(currentStep == numCandles){

    if(mistakesMade>0) {
      digitalWrite(failPin, HIGH);
      delay(2000);
      reset();
      }
    else {
      Serial.println(F("Puzzle Solved!"));
      onSolve();
      reset();
    }
  }
}
