/**
 * "Blow Out Candles" Escape Room Puzzle
 * Copyright (c) 2021 Playful Technology
 *
 * Players must blow out candles, as detected by microphone sound sensors.
 * Doing so "extinguishes" them, turning off an LED flame.
 * - If they blow out all candles in the correct order, a relay is energised (e.g. releasing a maglock)
 * - If they blow out candles in the incorrect order, they reignite and the puzzle is reset
 *
 * The puzzle is activated when a specific RFID tag is detected by the MFRC522 reader.
 * If the tag is removed, the puzzle resets and waits for the tag to be placed back.
 */

// INCLUDES
#include <Arduino.h>
#include <SPI.h>
#include <MFRC522.h>

#define SS_PIN 10
#define RST_PIN 9
 
MFRC522 rfid(SS_PIN, RST_PIN); // Instance of the class

// Init array that will store new NUID 
byte nuidPICC[4];


/**
 * Helper routine to dump a byte array as hex values to Serial. 
 */
void printHex(byte *buffer, byte bufferSize) {
  for (byte i = 0; i < bufferSize; i++) {
    Serial.print(buffer[i] < 0x10 ? " 0" : " ");
    Serial.print(buffer[i], HEX);
  }
}

/**
 * Helper routine to dump a byte array as dec values to Serial.
 */
void printDec(byte *buffer, byte bufferSize) {
  for (byte i = 0; i < bufferSize; i++) {
    Serial.print(' ');
    Serial.print(buffer[i], DEC);
  }
}

void setup() { 
  Serial.begin(9600);
  SPI.begin(); // Init SPI bus
  rfid.PCD_Init(); // Init MFRC522 

  Serial.println(F("This code scan the MIFARE Classsic NUID."));
}
 
void loop() {

  // Reset the loop if no new card present on the sensor/reader. This saves the entire process when idle.
  if ( ! rfid.PICC_IsNewCardPresent())
    return;

  // Verify if the NUID has been readed
  if ( ! rfid.PICC_ReadCardSerial())
    return;

  Serial.print(F("PICC type: "));
  MFRC522::PICC_Type piccType = rfid.PICC_GetType(rfid.uid.sak);
  Serial.println(rfid.PICC_GetTypeName(piccType));

  // Check is the PICC of Classic MIFARE type
  if (piccType != MFRC522::PICC_TYPE_MIFARE_MINI &&  
    piccType != MFRC522::PICC_TYPE_MIFARE_1K &&
    piccType != MFRC522::PICC_TYPE_MIFARE_4K) {
    Serial.println(F("Your tag is not of type MIFARE Classic."));
    return;
  }

  if (rfid.uid.uidByte[0] != nuidPICC[0] || 
    rfid.uid.uidByte[1] != nuidPICC[1] || 
    rfid.uid.uidByte[2] != nuidPICC[2] || 
    rfid.uid.uidByte[3] != nuidPICC[3] ) {
    Serial.println(F("A new card has been detected."));

    // Store NUID into nuidPICC array
    for (byte i = 0; i < 4; i++) {
      nuidPICC[i] = rfid.uid.uidByte[i];
    }
   
    Serial.println(F("The NUID tag is:"));
    Serial.print(F("In hex: "));
    printHex(rfid.uid.uidByte, rfid.uid.size);
    Serial.println();
    Serial.print(F("In dec: "));
    printDec(rfid.uid.uidByte, rfid.uid.size);
    Serial.println();
  }
  else Serial.println(F("Card read previously."));

  // Halt PICC
  rfid.PICC_HaltA();

  // Stop encryption on PCD
  rfid.PCD_StopCrypto1();
}

// // CONSTANTS
// const byte numCandles = 3;
// // Define the GPIO pins connected to the anode of each candle's LED
// // To create flickering effect, this pin must support PWM (UNO/Nano pins 3, 5, 6, 9, 10 and 11)
// const byte ledPins[] = {6, 5, 3};
// // Define the pins connected to the sound sensor inputs. These can be any GPIO pins.
// const byte inputPins[] = {7, 4, 2};
// // Define the order in which candles should be blown out
// const byte order[] = {0, 1, 2};
// // This pin will be driven HIGH to release a lock when puzzle is solved
// const byte lockPin = A3;
// const byte failPin = A4;

// // MFRC522 RFID Reader pins
// const byte RC522_RST = 9;  // Reset pin (reusing PN5180_RST)
// const byte RC522_SS = 10;   // SPI Slave Select (reusing PN5180_NSS)
// // Standard hardware SPI pins for Arduino Uno
// // SCK = 13, MOSI = 11, MISO = 12 (these are fixed for hardware SPI)

// // Target RFID tag UID (8 bytes for ISO15693)
// // Replace with your specific tag's UID
// byte targetTagUID[4] = {0x00, 0x00, 0x00, 0x00};

// // GLOBALS
// // MFRC522 RFID reader object
// MFRC522 rfid(RC522_SS, RC522_RST);

// // What step of the sequence is the player currently on?
// int currentStep = 0;
// bool isLit[numCandles];
// int mistakesMade = 0;

// // Puzzle state variables
// bool puzzleActive = false;
// bool tagPresent = false;
// unsigned long lastTagCheck = 0;
// const unsigned long TAG_CHECK_INTERVAL = 500; // Check for tag every 500ms

// void reset();
// void onSolve();
// bool checkForTargetTag();
// void activatePuzzle();
// void deactivatePuzzle();

// void setup() {
//   Serial.begin(9600);

//   for(int i=0; i<numCandles; i++){
//     pinMode(inputPins[i], INPUT);
//     pinMode(ledPins[i], OUTPUT);
//   }

//   pinMode(lockPin, OUTPUT);
//   pinMode(failPin, OUTPUT);

//   SPI.begin();
//   rfid.PCD_Init();

//   targetTagUID[1] = 0x00;
//   targetTagUID[2] = 0x00;
//   targetTagUID[3] = 0x00;
//   deactivatePuzzle();
// }

// void onSolve() {
//   // Activate the relay to supply power to the maglock
//   digitalWrite(lockPin, HIGH);
//   // Wait for one second
//   delay(500);
//   // De-activate the relay again
//   digitalWrite(lockPin, LOW);
// }


// void reset() {
//   // Only reset if puzzle is active
//   if (puzzleActive) {
//     // (Re)light all the candles
//     digitalWrite(failPin, LOW);
//     for(int i=0; i< numCandles; i++){
//       isLit[i] = true;
//       digitalWrite(ledPins[i], HIGH);
//       delay(100);
//     }
//     // Reset the counters
//     currentStep = 0;
//     mistakesMade = 0;

//     Serial.println(F("Puzzle Reset!"));
//   } else {
//     // If puzzle is inactive, ensure all candles are off
//     for(int i=0; i< numCandles; i++){
//       isLit[i] = false;
//       digitalWrite(ledPins[i], LOW);
//     }
//     digitalWrite(failPin, LOW);
//   }
// }


// void loop() {
//   // Check for RFID tag periodically
//   if (millis() - lastTagCheck >= TAG_CHECK_INTERVAL) {
//     lastTagCheck = millis();

//     bool currentTagPresent = checkForTargetTag();

//     if (currentTagPresent != tagPresent) {
//       tagPresent = currentTagPresent;

//       if (tagPresent) {
//         activatePuzzle();
//       } else {
//         deactivatePuzzle();
//       }
//     }
//   }

//   // Only run puzzle logic if puzzle is active
//   if (puzzleActive) {
//     // Loop through all the inputs
//     for(int i=0; i<numCandles; i++){

//       // Is this candle currently still alight?
//       if(isLit[i]){

//         // Make the LED flicker
//         analogWrite(ledPins[i], random(128, 255));

//         // Is this candle being blown?
//         if(digitalRead(inputPins[i]) == LOW) {

//           // Simple debounce to guard against false readings
//           // Wait for short amount of time
//           delay(20);
//           // And check whether the candle is *still* being blown
//           if(digitalRead(inputPins[i]) == LOW) {

//             // Extinguish the flame
//             isLit[i] = false;
//             digitalWrite(ledPins[i], LOW);

//             // Was this the correct candle to blow out next in the sequence?
//             if(order[currentStep] == i) {
//               Serial.print(F("Candle #"));
//               Serial.print(i);
//               Serial.println(F(" correctly extinguished!"));
//             }
//             else {
//               Serial.print(F("Candle #"));
//               Serial.print(i);
//               Serial.print(F(" incorrectly extinguished!"));
//               Serial.print(F(" (should have been "));
//               Serial.print(order[currentStep]);
//               Serial.println(")");

//               mistakesMade++;
//             }

//             currentStep++;
//           }
//         }
//       }
//     }

//     if(currentStep == numCandles){
//       if(mistakesMade>0) {
//         digitalWrite(failPin, HIGH);
//         delay(2000);
//         reset();
//       }
//       else {
//         Serial.println(F("Puzzle Solved!"));
//         onSolve();
//         reset();
//       }
//     }
//   }
// }

// // Check if the target RFID tag is present
// bool checkForTargetTag() {
//   Serial.println(F("checking for tag"));
//   uint8_t uid[8];

//   // Try to read a tag - getInventory returns number of tags detected
//   if ( ! rfid.PICC_IsNewCardPresent()) {
//     return false;
//   }

//   // Verify if the tag is valid
//   if ( ! rfid.PICC_ReadCardSerial()) {
//     return false;
//   }
//   Serial.println(F("tag detected"));

//   // Tag detected, check if it matches our target
//   bool isTargetTag = true;
//   for (int i = 0; i < 4; i++) {
//     if (uid[i] != targetTagUID[i]) {
//       isTargetTag = false;
//         break;
//     }
//   }

//     if (isTargetTag) {
//       return true;
//     } else {
//       Serial.print(F("Wrong tag detected: "));
//       for (int i = 7; i >= 0; i--) { // Print in reverse order (MSB first)
//         if(uid[i] < 0x10) Serial.print("0");
//         Serial.print(uid[i], HEX);
//       }
//       Serial.println();
//       return false;
//     }
//   }

// // Activate the puzzle when correct tag is detected
// void activatePuzzle() {
//   if (!puzzleActive) {
//     Serial.println(F("Target RFID tag detected! Activating puzzle..."));
//     puzzleActive = true;
//     reset(); // This will light up the candles since puzzleActive is now true
//   }
// }

// // Deactivate the puzzle when tag is removed
// void deactivatePuzzle() {
//   if (puzzleActive) {
//     Serial.println(F("RFID tag removed! Deactivating puzzle..."));
//   }
//   puzzleActive = false;
//   reset(); // This will turn off all candles since puzzleActive is now false
// }
