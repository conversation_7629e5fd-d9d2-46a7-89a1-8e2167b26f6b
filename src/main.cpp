/**
 * "Blow Out Candles" Escape Room Puzzle
 * Copyright (c) 2021 Playful Technology
 *
 * Players must blow out candles, as detected by microphone sound sensors.
 * Doing so "extinguishes" them, turning off an LED flame.
 * - If they blow out all candles in the correct order, a relay is energised (e.g. releasing a maglock)
 * - If they blow out candles in the incorrect order, they reignite and the puzzle is reset
 *
 * The puzzle is activated when a specific RFID tag is detected by the PN5180 reader.
 * If the tag is removed, the puzzle resets and waits for the tag to be placed back.
 */

// INCLUDES
#include <Arduino.h>
#include <SPI.h>
#include <PN5180.h>

// CONSTANTS
const byte numCandles = 4;
// Define the GPIO pins connected to the anode of each candle's LED
// To create flickering effect, this pin must support PWM (UNO/Nano pins 3, 5, 6, 9, 10 and 11)
const byte ledPins[] = {9, 6, 5, 3};
// Define the pins connected to the sound sensor inputs. These can be any GPIO pins.
const byte inputPins[] = {8, 7, 4, 2};
// Define the order in which candles should be blown out
const byte order[] = {0, 1, 2, 3};
// This pin will be driven HIGH to release a lock when puzzle is solved
const byte lockPin = A3;
const byte failPin = A4;

// PN5180 RFID Reader pins
const byte PN5180_NSS = A1;   // SPI Slave Select
const byte PN5180_BUSY = A0;  // Busy pin
const byte PN5180_RST = A2;   // Reset pin
// Standard hardware SPI pins for Arduino Uno
const byte PN5180_SCK = 13;   // SPI Clock (standard pin)
const byte PN5180_MOSI = 11;  // SPI Master Out Slave In (standard pin)
const byte PN5180_MISO = 12;  // SPI Master In Slave Out (standard pin)

// Target RFID tag UID (8 bytes for ISO15693)
// Replace with your specific tag's UID
byte targetTagUID[8] = {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};

// GLOBALS
// PN5180 RFID reader object
PN5180 nfc(PN5180_NSS, PN5180_BUSY, PN5180_RST);

// What step of the sequence is the player currently on?
int currentStep = 0;
bool isLit[numCandles];
int mistakesMade = 0;

// Puzzle state variables
bool puzzleActive = false;
bool tagPresent = false;
unsigned long lastTagCheck = 0;
const unsigned long TAG_CHECK_INTERVAL = 500; // Check for tag every 500ms

void reset();
void onSolve();
bool checkForTargetTag();
void activatePuzzle();
void deactivatePuzzle();

void setup() {
  // Initialize serial communication
  Serial.begin(115200);
  Serial.println(__FILE__ __DATE__);

  // Initialise the input pins that have switches attached
  for(int i=0; i<numCandles; i++){
    pinMode(inputPins[i], INPUT);
    pinMode(ledPins[i], OUTPUT);
  }

  // Set the lock pin as output and secure the lock
  pinMode(lockPin, OUTPUT);
  pinMode(failPin, OUTPUT);

  // Initialize PN5180 control pins
  pinMode(PN5180_NSS, OUTPUT);   // NSS = Pin A1
  pinMode(PN5180_RST, OUTPUT);   // RST = Pin A2
  pinMode(PN5180_BUSY, INPUT);   // BUSY = Pin A0

  // Set initial states for control pins
  digitalWrite(PN5180_NSS, HIGH);  // NSS idle high
  digitalWrite(PN5180_RST, HIGH);  // RST idle high
  

  // Initialize hardware SPI (pins 11, 12, 13 are automatically configured)
  SPI.begin();

  // Initialize PN5180
  Serial.println(F("Initializing PN5180..."));
  Serial.println(F("Wiring: NSS=A1, BUSY=A0, RST=A2, SCK=13, MOSI=11, MISO=12"));
  nfc.begin();

  // Set the target tag UID - replace with your actual tag UID
  // To find your tag's UID, upload this code and check the serial monitor
  // when you place your tag on the reader. It will show "Wrong tag detected: XXXXXXXX"
  // Copy those hex values into the array below (in reverse order)
  targetTagUID[0] = 0xE0; // Example values - replace with your actual tag UID
  targetTagUID[1] = 0x04;
  targetTagUID[2] = 0x01;
  targetTagUID[3] = 0x00;
  targetTagUID[4] = 0x00;
  targetTagUID[5] = 0x00;
  targetTagUID[6] = 0x00;
  targetTagUID[7] = 0x00;

  Serial.println(F("PN5180 initialized. Waiting for target RFID tag..."));

  // Start with puzzle inactive
  deactivatePuzzle();
}

void onSolve() {
  // Activate the relay to supply power to the maglock
  digitalWrite(lockPin, HIGH);
  // Wait for one second
  delay(500);
  // De-activate the relay again
  digitalWrite(lockPin, LOW);
}


void reset() {
  // Only reset if puzzle is active
  if (puzzleActive) {
    // (Re)light all the candles
    digitalWrite(failPin, LOW);
    for(int i=0; i< numCandles; i++){
      isLit[i] = true;
      digitalWrite(ledPins[i], HIGH);
      delay(100);
    }
    // Reset the counters
    currentStep = 0;
    mistakesMade = 0;

    Serial.println(F("Puzzle Reset!"));
  } else {
    // If puzzle is inactive, ensure all candles are off
    for(int i=0; i< numCandles; i++){
      isLit[i] = false;
      digitalWrite(ledPins[i], LOW);
    }
    digitalWrite(failPin, LOW);
  }
}


void loop() {
  // Check for RFID tag periodically
  if (millis() - lastTagCheck >= TAG_CHECK_INTERVAL) {
    lastTagCheck = millis();

    bool currentTagPresent = checkForTargetTag();

    // Tag state changed
    if (currentTagPresent != tagPresent) {
      tagPresent = currentTagPresent;

      if (tagPresent) {
        // Tag detected - activate puzzle
        activatePuzzle();
      } else {
        // Tag removed - deactivate puzzle
        deactivatePuzzle();
      }
    }
  }

  // Only run puzzle logic if puzzle is active
  if (puzzleActive) {
    // Loop through all the inputs
    for(int i=0; i<numCandles; i++){

      // Is this candle currently still alight?
      if(isLit[i]){

        // Make the LED flicker
        analogWrite(ledPins[i], random(128, 255));

        // Is this candle being blown?
        if(digitalRead(inputPins[i]) == LOW) {

          // Simple debounce to guard against false readings
          // Wait for short amount of time
          delay(20);
          // And check whether the candle is *still* being blown
          if(digitalRead(inputPins[i]) == LOW) {

            // Extinguish the flame
            isLit[i] = false;
            digitalWrite(ledPins[i], LOW);

            // Was this the correct candle to blow out next in the sequence?
            if(order[currentStep] == i) {
              Serial.print(F("Candle #"));
              Serial.print(i);
              Serial.println(F(" correctly extinguished!"));
            }
            else {
              Serial.print(F("Candle #"));
              Serial.print(i);
              Serial.print(F(" incorrectly extinguished!"));
              Serial.print(F(" (should have been "));
              Serial.print(order[currentStep]);
              Serial.println(")");

              mistakesMade++;
            }

            currentStep++;
          }
        }
      }
    }

    if(currentStep == numCandles){
      if(mistakesMade>0) {
        digitalWrite(failPin, HIGH);
        delay(2000);
        reset();
      }
      else {
        Serial.println(F("Puzzle Solved!"));
        onSolve();
        reset();
      }
    }
  }
}

// Check if the target RFID tag is present
bool checkForTargetTag() {
  uint8_t uid[8];

  // Try to read a tag - getInventory returns number of tags detected
  uint8_t numberOfTags = nfc.getInventory(uid);

  if (numberOfTags > 0) {
    // Tag detected, check if it matches our target
    bool isTargetTag = true;
    for (int i = 0; i < 8; i++) {
      if (uid[i] != targetTagUID[i]) {
        isTargetTag = false;
        break;
      }
    }

    if (isTargetTag) {
      return true;
    } else {
      // Wrong tag detected
      Serial.print(F("Wrong tag detected: "));
      for (int i = 7; i >= 0; i--) { // Print in reverse order (MSB first)
        if(uid[i] < 0x10) Serial.print("0");
        Serial.print(uid[i], HEX);
      }
      Serial.println();
      return false;
    }
  }

  // No tag detected
  return false;
}

// Activate the puzzle when correct tag is detected
void activatePuzzle() {
  if (!puzzleActive) {
    Serial.println(F("Target RFID tag detected! Activating puzzle..."));
    puzzleActive = true;
    reset(); // This will light up the candles since puzzleActive is now true
  }
}

// Deactivate the puzzle when tag is removed
void deactivatePuzzle() {
  if (puzzleActive) {
    Serial.println(F("RFID tag removed! Deactivating puzzle..."));
  }
  puzzleActive = false;
  reset(); // This will turn off all candles since puzzleActive is now false
}