#include "RfidReader.h"

//   byte sck = 18;    // Green
//   byte mosi = 23;   // Blue
//   byte miso = 19;   // Purple

RfidReader::RfidReader(byte nssPin, byte rstPin, const byte* uid)
  : reader(nssPin, rstPin), allowedUidSize(4), lastCardSeen(0) {
  memcpy(allowedUid, uid, allowedUidSize);
}

void RfidReader::begin() {
  reader.PCD_Init();
}

bool RfidReader::tagDetected() {
  if (reader.PICC_IsNewCardPresent() && reader.PICC_ReadCardSerial()) {
    if (reader.uid.size == allowedUidSize &&
        memcmp(reader.uid.uidByte, allowedUid, allowedUidSize) == 0) {
      lastCardSeen = millis();
      return true;
    }
  }
  return false;
}

bool RfidReader::tagRemoved() {
  return millis() - lastCardSeen > 1000;
}

void RfidReader::displayDetectedTag() {
  Serial.print(F("Tag detected: "));
  for (byte i = 0; i < reader.uid.size; i++) {
    Serial.print(reader.uid.uidByte[i] < 0x10 ? " 0" : " ");
    Serial.print(reader.uid.uidByte[i], HEX);
  }
  Serial.println();
}
