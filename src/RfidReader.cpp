#include "RfidReader.h"

//   byte sck = 18;    // Green
//   byte mosi = 23;   // Blue
//   byte miso = 19;   // Purple

RfidReader::RfidReader(byte ssPin, byte rstPin, byte* targetTagUid, byte tagSize, unsigned long timeout)
  : mfrc522(ssPin, rstPin), targetTag(targetTagUid), targetTagSize(tagSize), timeoutMs(timeout), lastSeenTime(0) {
}

void RfidReader::begin() {
  SPI.begin();
  mfrc522.PCD_Init();
  Serial.println(F("RFID Reader initialized"));
}

bool RfidReader::isTargetTagPresent() {
  if (mfrc522.PICC_IsNewCardPresent() && mfrc522.PICC_ReadCardSerial()) {
    if (isTargetTag()) {
      Serial.println(F("Correct tag detected!"));
      updateLastSeenTime();
      mfrc522.PICC_HaltA();
      mfrc522.PCD_StopCrypto1();
      return true;
    } else {
      displayDetectedTag();
      mfrc522.PICC_HaltA();
      mfrc522.PCD_StopCrypto1();
      return false;
    }
  }

  // If we had a tag before and it hasn't timed out, consider it still present
  return (lastSeenTime > 0) && !hasTimedOut();
}

bool RfidReader::hasTimedOut() {
  return (lastSeenTime > 0) && (millis() - lastSeenTime > timeoutMs);
}

void RfidReader::displayDetectedTag() {
  Serial.print(F("Wrong tag detected: "));
  printHexBytes(mfrc522.uid.uidByte, mfrc522.uid.size);
  Serial.println();
}

void RfidReader::printHexBytes(byte* buffer, byte bufferSize) {
  for (byte i = 0; i < bufferSize; i++) {
    Serial.print(buffer[i] < 0x10 ? " 0" : " ");
    Serial.print(buffer[i], HEX);
  }
}

bool RfidReader::isTargetTag() {
  for (int i = 0; i < targetTagSize; i++) {
    if (mfrc522.uid.uidByte[i] != targetTag[i]) {
      return false;
    }
  }
  return true;
}

void RfidReader::updateLastSeenTime() {
  lastSeenTime = millis();
}
